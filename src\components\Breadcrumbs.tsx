import React from "react";
import { Link, useLocation } from "react-router-dom";
import { ChevronRight } from "lucide-react";

interface BreadcrumbItem {
  label: string;
  path: string;
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
}

/**
 * Breadcrumbs component for navigation
 * Automatically generates breadcrumbs based on the current path if no items are provided
 */
const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
  const location = useLocation();

  // If no items are provided, generate them from the current path
  const breadcrumbItems =
    items || generateBreadcrumbsFromPath(location.pathname);

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav className="flex items-center text-sm mb-4" aria-label="Breadcrumb">
      <ol className="flex items-center flex-wrap">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;

          return (
            <li key={`${item.path}-${index}`} className="flex items-center">
              {index > 0 && (
                <ChevronRight size={14} className="mx-2 text-dark-400" />
              )}

              {isLast ? (
                <span className="text-dark-200 font-medium">{item.label}</span>
              ) : (
                <Link
                  to={item.path}
                  className="text-dark-400 hover:text-primary-400 transition-colors"
                >
                  {item.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

/**
 * Generate breadcrumb items from a path
 * @param path The current path
 * @returns Array of breadcrumb items
 */
const generateBreadcrumbsFromPath = (path: string): BreadcrumbItem[] => {
  const parts = path.split("/").filter(Boolean);
  const items: BreadcrumbItem[] = [];

  // Add home
  items.push({
    label: "Dashboard",
    path: "/dashboard",
  });

  // Build the breadcrumb items
  let currentPath = "";

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    currentPath += `/${part}`;

    // Skip the dashboard part as it's already added
    if (part === "dashboard") continue;

    // Special handling for accounts section to avoid duplication
    if (part === "accounts") {
      // Don't add a breadcrumb for "accounts" since we'll handle it with the ID
      continue;
    }

    // Handle dynamic segments
    if (part.match(/^\d+$/) || part.startsWith(":")) {
      // This is a dynamic segment (ID)
      const previousPart = parts[i - 1];

      if (previousPart === "scans") {
        items.push({
          label: `Scan #${part}`,
          path: currentPath,
        });
      } else if (previousPart === "accounts") {
        // Add the "Accounts" breadcrumb that links to the dashboard
        // Add a unique identifier to the path to avoid duplicate keys
        items.push({
          label: "Accounts",
          path: "/dashboard?section=accounts", // Add query param to make the path unique while still linking to dashboard
        });
        // Then add the account details breadcrumb
        items.push({
          label: `Account #${part}`,
          path: currentPath,
        });
      } else if (previousPart === "findings") {
        items.push({
          label: `Finding #${part}`,
          path: currentPath,
        });
      } else {
        items.push({
          label: `#${part}`,
          path: currentPath,
        });
      }
    } else {
      // Regular segment (but skip adding "accounts" as we handle it specially)
      items.push({
        label: capitalizeFirstLetter(part),
        path: currentPath,
      });
    }
  }

  return items;
};

/**
 * Capitalize the first letter of a string
 * @param str The string to capitalize
 * @returns The capitalized string
 */
const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export default Breadcrumbs;
