# Production Environment Configuration
# This file contains production-specific environment variables

# =============================================================================
# API Configuration
# =============================================================================

# Production Backend API Base URL
VITE_BACKEND_LOCAL_BASE_URL=http://192.168.2.112:8000

# API Timeout (30 seconds for production)
VITE_API_TIMEOUT=30000

# Maximum retry attempts for failed requests
VITE_MAX_RETRIES=3

# =============================================================================
# Application Configuration
# =============================================================================

# Application Environment
VITE_APP_ENV=production

# Application Name
VITE_APP_NAME=CloudAudit

# Application Version (set during CI/CD)
VITE_APP_VERSION=${npm_package_version}

# Application Base URL
VITE_APP_BASE_URL=https://app.cloudaudit.com

# =============================================================================
# Feature Flags
# =============================================================================

# Disable React Query DevTools in production
VITE_ENABLE_DEVTOOLS=false

# Disable debug logging in production
VITE_ENABLE_DEBUG_LOGGING=false

# Enable performance monitoring in production
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Enable error reporting in production
VITE_ENABLE_ERROR_REPORTING=true

# =============================================================================
# Security Configuration
# =============================================================================

# Token refresh threshold (5 minutes before expiry)
VITE_TOKEN_REFRESH_THRESHOLD=5

# Session timeout (60 minutes)
VITE_SESSION_TIMEOUT=60

# =============================================================================
# Third-party Services
# =============================================================================

# Sentry DSN for error reporting (replace with actual DSN)
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# Google Analytics ID (replace with actual GA ID)
VITE_GA_ID=G-XXXXXXXXXX

# =============================================================================
# Build Configuration
# =============================================================================

# Disable source maps in production for security
VITE_ENABLE_SOURCE_MAPS=false

# Disable bundle analysis in production
VITE_ENABLE_BUNDLE_ANALYSIS=false

# Build output directory
VITE_BUILD_OUTPUT_DIR=dist

# =============================================================================
# CDN Configuration
# =============================================================================

# CDN Base URL for static assets (replace with actual CDN URL)
VITE_CDN_BASE_URL=https://cdn.cloudaudit.com

# =============================================================================
# Monitoring and Analytics
# =============================================================================

# Enable web vitals reporting in production
VITE_ENABLE_WEB_VITALS=true

# Performance monitoring endpoint (replace with actual endpoint)
VITE_PERFORMANCE_ENDPOINT=https://analytics.cloudaudit.com/performance
