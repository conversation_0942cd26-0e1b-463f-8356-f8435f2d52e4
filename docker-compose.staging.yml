# Docker Compose configuration for CloudAudit Frontend - Staging Environment
version: '3.8'

services:
  # Frontend Application - Staging
  cloudaudit-frontend-staging:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        BUILD_MODE: staging
        VITE_BACKEND_LOCAL_BASE_URL: ${VITE_BACKEND_LOCAL_BASE_URL:-https://api-staging.cloudaudit.com/api}
        VITE_APP_ENV: ${VITE_APP_ENV:-staging}
        VITE_APP_NAME: ${VITE_APP_NAME:-CloudAudit (Staging)}
        VITE_APP_BASE_URL: ${VITE_APP_BASE_URL:-https://staging.cloudaudit.com}
        VITE_SENTRY_DSN: ${VITE_SENTRY_DSN}
        VITE_GA_ID: ${VITE_GA_ID}
        VITE_CDN_BASE_URL: ${VITE_CDN_BASE_URL}
    container_name: cloudaudit-frontend-staging
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
    networks:
      - cloudaudit-staging-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cloudaudit-frontend-staging.rule=Host(`staging.cloudaudit.com`)"
      - "traefik.http.routers.cloudaudit-frontend-staging.tls=true"
      - "traefik.http.routers.cloudaudit-frontend-staging.tls.certresolver=letsencrypt"
      - "traefik.http.services.cloudaudit-frontend-staging.loadbalancer.server.port=80"
    volumes:
      # Optional: Mount logs for external log aggregation
      - ./logs/nginx-staging:/var/log/nginx:rw
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

networks:
  cloudaudit-staging-network:
    driver: bridge
    name: cloudaudit-staging-network
