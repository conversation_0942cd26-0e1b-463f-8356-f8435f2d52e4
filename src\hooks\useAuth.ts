import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { login, signup, logout, getUserInfo, refreshToken, AuthError, AuthErrorType } from '@services/auth';
import type { LoginRequest, SignupRequest } from '@/types/api';
import { useNavigate, useLocation } from 'react-router-dom';
import { ServiceError, ServiceErrorType } from '@services/baseService';

export const useAuth = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const location = useLocation();

    const { data: user, isLoading: isUserLoading } = useQuery({
        queryKey: ['user'],
        queryFn: async () => {
            const token = localStorage.getItem('access_token');
            if (!token) return null;

            try {
                const response = await getUserInfo();
                return response.data;
            } catch (error) {
                // Handle different error types
                if (error instanceof AuthError) {
                    if (error.type === AuthErrorType.UNAUTHORIZED ||
                        error.type === AuthErrorType.TOKEN_EXPIRED) {

                        // Try to refresh the token if it's expired
                        try {
                            await refreshToken();
                            // If refresh succeeds, try to get user info again
                            const response = await getUserInfo();
                            return response.data;
                        } catch {
                            // If refresh fails, clear tokens and return null
                            localStorage.clear();
                            return null;
                        }
                    }
                }

                // For other errors, just return null
                return null;
            }
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: false, // Don't retry on error
    });

    const loginMutation = useMutation({
        mutationFn: (data: LoginRequest) => login(data),
        onSuccess: async () => {
            // Fetch user info after successful login
            const userInfo = await getUserInfo();
            queryClient.setQueryData(['user'], userInfo.data);

            // Navigate to the return URL or dashboard
            const from = (location.state as { from?: { pathname?: string } })?.from?.pathname || '/dashboard';
            navigate(from, { replace: true });
        },
        onError: (error: Error) => {
            if (error instanceof AuthError) {
                throw new Error(error.message);
            }
            throw new Error('An unexpected error occurred. Please try again.');
        }
    });

    const signupMutation = useMutation({
        mutationFn: (data: SignupRequest) => signup(data),
        onSuccess: (response, variables) => {
            // Navigate with credentials
            navigate('/login', {
                state: {
                    email: variables.email,
                    message: 'Account created successfully! Please login to continue.'
                },
                replace: true
            });
        },
        onError: (error: Error) => {
            if (error instanceof AuthError) {
                throw new Error(error.message);
            }
            throw new Error('An unexpected error occurred while creating your account.');
        }
    });

    const logoutMutation = useMutation({
        mutationFn: () => logout(),
        onSuccess: () => {
            queryClient.clear();
            navigate('/login', { replace: true });
        },
        onError: () => {
            queryClient.clear();
            navigate('/login', { replace: true });
        }
    });

    const refreshTokenMutation = useMutation({
        mutationFn: refreshToken,
        onSuccess: async () => {
            // Fetch user info after successful token refresh
            try {
                const userInfo = await getUserInfo();
                queryClient.setQueryData(['user'], userInfo.data);
            } catch {
                // If we can't get user info after token refresh, logout
                logoutMutation.mutate();
            }
        },
        onError: (error: unknown) => {
            // If token refresh fails, redirect to login
            if (error instanceof ServiceError &&
                (error.type === ServiceErrorType.UNAUTHORIZED ||
                    error.type === ServiceErrorType.REFRESH_FAILED)) {
                // Clear any cached data
                queryClient.clear();

                // Redirect to login with a message
                navigate('/login', {
                    state: {
                        message: 'Your session has expired. Please sign in again.',
                        error: true
                    },
                    replace: true
                });
            }
        }
    });

    return {
        user,
        login: loginMutation,
        signup: signupMutation,
        logout: logoutMutation,
        refreshToken: refreshTokenMutation,
        isAuthenticated: !!user,
        isLoading: isUserLoading
    };
};