import { FindingDetail, FindingDetailItem, RemediationInfo } from "@/types/api";

/**
 * Type for remediation data passed to the API
 */
export type RemediationData = FindingDetailItem;

/**
 * Props for the FindingSummary component
 */
export interface FindingSummaryProps {
  finding: FindingDetail;
}

/**
 * Props for the FindingDetailsCard component
 */
export interface FindingDetailsCardProps {
  finding: FindingDetail;
}

/**
 * Props for the FindingDetailsTable component
 */
export interface FindingDetailsTableProps {
  finding: FindingDetail;
  onRemediate: (findingId: number, detailIndex: number, detailData: RemediationData) => Promise<void>;
  remediatingDetailIndex: number | null;
  isPending: boolean;
  onViewRemediationDetails: (detail: FindingDetailItem, index: number) => void;
}

/**
 * Props for the FindingDetailsRow component
 */
export interface FindingDetailsRowProps {
  detail: FindingDetailItem;
  index: number;
  fieldLabels: Record<string, string>;
  findingId: number;
  onRemediate: (findingId: number, detailIndex: number, detailData: RemediationData) => Promise<void>;
  remediatingDetailIndex: number | null;
  isPending: boolean;
  onViewRemediationDetails: (detail: FindingDetailItem, index: number) => void;
}

/**
 * Props for the RemediationButton component
 */
export interface RemediationButtonProps {
  findingId: number;
  detailIndex: number;
  detail: FindingDetailItem;
  onRemediate: (findingId: number, detailIndex: number, detailData: RemediationData) => Promise<void>;
  isRemediating: boolean;
}

/**
 * Props for the RemediationStatusButton component
 */
export interface RemediationStatusButtonProps {
  remediate: RemediationInfo;
  onClick: () => void;
}

/**
 * Props for the RemediationDetailsModal component
 */
export interface RemediationDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  detail: FindingDetailItem;
  detailIndex: number;
  findingId: number;
  onRemediate: (findingId: number, detailIndex: number, detailData: RemediationData) => Promise<void>;
  isRemediating: boolean;
}

/**
 * Props for the StatusBadge component
 */
export interface StatusBadgeProps {
  status: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Props for the SeverityBadge component
 */
export interface SeverityBadgeProps {
  severity: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Props for the BooleanIndicator component
 */
export interface BooleanIndicatorProps {
  value: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Props for the ErrorState component
 */
export interface ErrorStateProps {
  error: unknown;
  onRetry?: () => void;
  onBack?: () => void;
}

/**
 * Props for the LoadingState component
 */
export interface LoadingStateProps {
  message?: string;
}

/**
 * Remediation status type
 */
export type RemediationStatus = 'pass' | 'fail' | 'failed' | 'pending' | 'remediated';

/**
 * Helper function to determine the remediation status
 */
export function getRemediationStatus(detail: FindingDetailItem): RemediationStatus {
  return detail.remediate?.status as RemediationStatus || 'pending';
}

/**
 * Helper function to get the remediation message
 */
export function getRemediationMessage(detail: FindingDetailItem): string {
  const status = getRemediationStatus(detail);

  return detail.remediate?.message ||
    (status === "pass"
      ? "Remediation completed successfully."
      : status === "failed" || status === "fail"
        ? "Remediation failed. Please try again."
        : status === "remediated"
          ? "This finding has been remediated."
          : "Remediation is in progress...");
}
