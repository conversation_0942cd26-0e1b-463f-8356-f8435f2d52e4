import { cn } from "@/lib/utils";
import React from "react";

export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Additional props can be added here if needed */
  variant?: "default" | "circular" | "rectangular";
}

/**
 * Skeleton component for loading states
 * @param props Component props
 * @returns Skeleton component
 */
export const Skeleton = ({ className, ...props }: SkeletonProps) => {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-dark-700/50", className)}
      {...props}
    />
  );
};
