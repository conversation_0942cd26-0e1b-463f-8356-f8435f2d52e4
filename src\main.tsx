import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import App from "./App";
import "./index.css";
import {
  config,
  validateEnvironment,
  isDevelopment,
} from "./config/environment";

// Validate environment configuration
const validation = validateEnvironment();
if (!validation.isValid) {
  console.error("Environment validation failed:", validation.errors);
  if (config.ENABLE_ERROR_REPORTING) {
    // In production, you might want to report this to your error tracking service
    validation.errors.forEach((error) => console.error(error));
  }
}

// Configure React Query with environment-specific settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: config.MAX_RETRIES,
      staleTime: isDevelopment() ? 0 : 5 * 60 * 1000, // 5 minutes in production
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: false,
    },
  },
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <App />
        {config.ENABLE_DEVTOOLS && <ReactQueryDevtools initialIsOpen={false} />}
      </QueryClientProvider>
    </BrowserRouter>
  </StrictMode>
);
