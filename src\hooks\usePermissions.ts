import { useQuery } from '@tanstack/react-query';
import { getUserInfo } from '@services/auth';
import { ServiceError, ServiceErrorType } from '@services/baseService';
import { Permission, PermissionName, Role } from '@/types/permissions';
import { useCallback, useMemo } from 'react';

// Query key for permissions
export const PERMISSIONS_QUERY_KEY = ['permissions'];

/**
 * Hook to fetch and manage user permissions
 * @returns Object containing permission data and utility functions
 */
export const usePermissions = () => {
  // Fetch user info which contains roles and permissions
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: PERMISSIONS_QUERY_KEY,
    queryFn: async () => {
      try {
        const response = await getUserInfo();
        return response.data;
      } catch (error) {
        // Convert to ServiceError if it's not already
        if (!(error instanceof ServiceError)) {
          throw new ServiceError(
            ServiceErrorType.UNKNOWN_ERROR,
            error instanceof Error ? error.message : 'Failed to fetch user permissions',
            error
          );
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });

  // Extract roles and permissions from the response
  const roles = useMemo(() => data?.roles || [], [data]);
  const customRoles = useMemo(() => data?.custom_roles || [], [data]);

  // Combine all permissions from all roles
  const allPermissions = useMemo(() => {
    const permissionMap = new Map<number, Permission>();

    // Add permissions from standard roles
    roles.forEach(role => {
      role.permissions.forEach(permission => {
        permissionMap.set(permission.id, permission);
      });
    });

    // Add permissions from custom roles
    customRoles.forEach(role => {
      role.permissions.forEach(permission => {
        permissionMap.set(permission.id, permission);
      });
    });

    return Array.from(permissionMap.values());
  }, [roles, customRoles]);

  /**
   * Check if the user has a specific permission
   * @param permissionName The name of the permission to check
   * @returns True if the user has the permission, false otherwise
   */
  const hasPermission = useCallback(
    (permissionName: PermissionName): boolean => {
      if (!allPermissions.length) return false;

      return allPermissions.some(permission => permission.name === permissionName);
    },
    [allPermissions]
  );

  /**
   * Check if the user has all of the specified permissions
   * @param permissionNames Array of permission names to check
   * @returns True if the user has all permissions, false otherwise
   */
  const hasAllPermissions = useCallback(
    (permissionNames: PermissionName[]): boolean => {
      if (!allPermissions.length) return false;

      return permissionNames.every(permissionName =>
        hasPermission(permissionName)
      );
    },
    [allPermissions, hasPermission]
  );

  /**
   * Check if the user has any of the specified permissions
   * @param permissionNames Array of permission names to check
   * @returns True if the user has any of the permissions, false otherwise
   */
  const hasAnyPermission = useCallback(
    (permissionNames: PermissionName[]): boolean => {
      if (!allPermissions.length) return false;

      return permissionNames.some(permissionName =>
        hasPermission(permissionName)
      );
    },
    [allPermissions, hasPermission]
  );

  /**
   * Check if the user has a specific role
   * @param roleId The ID of the role to check
   * @returns True if the user has the role, false otherwise
   */
  const hasRole = useCallback(
    (roleId: number): boolean => {
      return roles.some(role => role.id === roleId) ||
        customRoles.some(role => role.id === roleId);
    },
    [roles, customRoles]
  );

  /**
   * Get all roles assigned to the user
   * @returns Array of all roles (standard and custom)
   */
  const getAllRoles = useCallback((): Role[] => {
    return [...roles, ...customRoles];
  }, [roles, customRoles]);

  return {
    roles,
    customRoles,
    allPermissions,
    isLoading,
    error,
    refetch,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    hasRole,
    getAllRoles,
    userData: data,
  };
};

export default usePermissions;
