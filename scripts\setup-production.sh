#!/bin/bash

# CloudAudit Frontend Production Setup Script
# This script sets up the production environment for CloudAudit Frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

print_status "Setting up CloudAudit Frontend Production Environment"

# Check system requirements
print_status "Checking system requirements..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    print_status "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    print_status "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if Git is installed
if ! command -v git &> /dev/null; then
    print_error "Git is not installed. Please install Git first."
    exit 1
fi

print_success "System requirements check passed"

# Create application directory
APP_DIR="/opt/cloudaudit-frontend"
print_status "Creating application directory: $APP_DIR"

if [[ ! -d "$APP_DIR" ]]; then
    sudo mkdir -p "$APP_DIR"
    sudo chown $USER:$USER "$APP_DIR"
    print_success "Application directory created"
else
    print_warning "Application directory already exists"
fi

# Clone or update repository
cd "$APP_DIR"
if [[ ! -d ".git" ]]; then
    print_status "Cloning repository..."
    git clone https://github.com/your-org/cloudaudit-frontend.git .
else
    print_status "Updating repository..."
    git pull origin main
fi

# Create environment file if it doesn't exist
if [[ ! -f ".env.production" ]]; then
    print_status "Creating production environment file..."
    cp .env.example .env.production
    
    print_warning "Please edit .env.production with your production configuration:"
    print_warning "  - VITE_BACKEND_LOCAL_BASE_URL"
    print_warning "  - VITE_APP_BASE_URL"
    print_warning "  - VITE_SENTRY_DSN"
    print_warning "  - VITE_GA_ID"
    print_warning "  - Other production-specific variables"
    
    read -p "Press Enter to continue after editing the environment file..."
fi

# Create logs directory
print_status "Creating logs directory..."
mkdir -p logs/nginx
sudo chown -R $USER:$USER logs/

# Create SSL certificates directory (for Let's Encrypt)
print_status "Creating SSL certificates directory..."
mkdir -p letsencrypt
sudo chown -R $USER:$USER letsencrypt/

# Set up firewall rules (if ufw is available)
if command -v ufw &> /dev/null; then
    print_status "Configuring firewall rules..."
    sudo ufw allow 22/tcp   # SSH
    sudo ufw allow 80/tcp   # HTTP
    sudo ufw allow 443/tcp  # HTTPS
    print_success "Firewall rules configured"
fi

# Create systemd service for auto-start
print_status "Creating systemd service..."
sudo tee /etc/systemd/system/cloudaudit-frontend.service > /dev/null <<EOF
[Unit]
Description=CloudAudit Frontend
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/docker-compose up -d
ExecStop=/usr/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable cloudaudit-frontend.service
print_success "Systemd service created and enabled"

# Set up log rotation
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/cloudaudit-frontend > /dev/null <<EOF
$APP_DIR/logs/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        docker-compose -f $APP_DIR/docker-compose.yml exec cloudaudit-frontend nginx -s reload
    endscript
}
EOF
print_success "Log rotation configured"

# Create backup script
print_status "Creating backup script..."
sudo tee /usr/local/bin/backup-cloudaudit-frontend.sh > /dev/null <<'EOF'
#!/bin/bash
# CloudAudit Frontend Backup Script

BACKUP_DIR="/opt/backups/cloudaudit-frontend"
APP_DIR="/opt/cloudaudit-frontend"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

# Backup configuration files
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" \
    -C "$APP_DIR" \
    .env.production \
    docker-compose.yml \
    nginx.conf \
    nginx-default.conf

# Keep only last 30 days of backups
find "$BACKUP_DIR" -name "config_*.tar.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/config_$DATE.tar.gz"
EOF

sudo chmod +x /usr/local/bin/backup-cloudaudit-frontend.sh
print_success "Backup script created"

# Set up cron job for backups
print_status "Setting up daily backup cron job..."
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-cloudaudit-frontend.sh") | crontab -
print_success "Daily backup cron job configured"

# Build and start the application
print_status "Building and starting the application..."
docker-compose build
docker-compose up -d

print_success "CloudAudit Frontend production environment setup completed!"

print_status "Next steps:"
print_status "1. Configure your domain DNS to point to this server"
print_status "2. Update .env.production with your actual domain and API endpoints"
print_status "3. Configure SSL certificates (Let's Encrypt recommended)"
print_status "4. Set up monitoring and alerting"
print_status "5. Configure backup storage (offsite recommended)"

print_status "Useful commands:"
print_status "  - View logs: docker-compose logs -f"
print_status "  - Restart: sudo systemctl restart cloudaudit-frontend"
print_status "  - Update: cd $APP_DIR && git pull && docker-compose build && docker-compose up -d"
print_status "  - Backup: /usr/local/bin/backup-cloudaudit-frontend.sh"
